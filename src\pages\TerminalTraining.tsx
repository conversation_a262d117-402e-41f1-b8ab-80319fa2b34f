import React, { useEffect } from 'react';
import Terminal from '@/components/Terminal';
import { Terminal as TerminalIcon, Brain, Zap, BookOpen, Shield, Code, Cpu } from 'lucide-react';

const TerminalTraining: React.FC = () => {
  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="pt-20 min-h-screen bg-psyco-black-DEFAULT">
      {/* Hero Section */}
      <section className="bg-psyco-black-light py-16 px-6 md:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-6">
              <TerminalIcon className="text-psyco-green-DEFAULT" size={48} />
              <h1 className="text-4xl md:text-5xl font-bold text-white animate-fade-in">
                🐺 Terminal Training System
              </h1>
            </div>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto animate-fade-in animation-delay-100">
              Master Linux commands with AI-powered interactive training. Learn, practice, and validate commands
              with real-time feedback from Wolf AI integration.
            </p>
            
            {/* Feature Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              <div className="glassmorphism p-6 text-center animate-fade-in animation-delay-200">
                <Brain className="text-psyco-green-DEFAULT mx-auto mb-4" size={32} />
                <h3 className="text-white font-semibold mb-2">AI-Powered Learning</h3>
                <p className="text-gray-300 text-sm">
                  Get intelligent feedback and explanations powered by Wolf AI
                </p>
              </div>
              
              <div className="glassmorphism p-6 text-center animate-fade-in animation-delay-300">
                <BookOpen className="text-psyco-green-DEFAULT mx-auto mb-4" size={32} />
                <h3 className="text-white font-semibold mb-2">Complete Command Database</h3>
                <p className="text-gray-300 text-sm">
                  Access 70+ Linux commands from A-Z with examples and documentation
                </p>
              </div>
              
              <div className="glassmorphism p-6 text-center animate-fade-in animation-delay-400">
                <Zap className="text-psyco-green-DEFAULT mx-auto mb-4" size={32} />
                <h3 className="text-white font-semibold mb-2">Interactive Training</h3>
                <p className="text-gray-300 text-sm">
                  Real-time command validation with security warnings and best practices
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Terminal Section */}
      <section className="py-16 px-6 md:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-4 text-center">
              Interactive Linux Terminal
            </h2>
            <p className="text-gray-300 text-center max-w-2xl mx-auto">
              Start typing commands below. Use built-in commands like 'help', 'training', or try any Linux command 
              to see documentation and examples. Enable training mode for AI-powered feedback.
            </p>
          </div>
          
          <Terminal />
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-6 md:px-12 bg-psyco-black-light">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-12 text-center">
            Training Features
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="glassmorphism p-6">
              <Shield className="text-psyco-green-DEFAULT mb-4" size={32} />
              <h3 className="text-white font-semibold mb-3">Security Focus</h3>
              <p className="text-gray-300 text-sm mb-4">
                Learn cybersecurity-focused commands with security warnings and best practices.
              </p>
              <ul className="text-gray-400 text-sm space-y-1">
                <li>• Permission management commands</li>
                <li>• Network security tools</li>
                <li>• System monitoring utilities</li>
                <li>• File encryption commands</li>
              </ul>
            </div>
            
            <div className="glassmorphism p-6">
              <Code className="text-psyco-green-DEFAULT mb-4" size={32} />
              <h3 className="text-white font-semibold mb-3">Command Categories</h3>
              <p className="text-gray-300 text-sm mb-4">
                Organized command database with multiple categories for structured learning.
              </p>
              <ul className="text-gray-400 text-sm space-y-1">
                <li>• File & Directory Operations</li>
                <li>• Text Processing & Search</li>
                <li>• System & Process Management</li>
                <li>• Network & Compression Tools</li>
              </ul>
            </div>
            
            <div className="glassmorphism p-6">
              <Cpu className="text-psyco-green-DEFAULT mb-4" size={32} />
              <h3 className="text-white font-semibold mb-3">AI Integration</h3>
              <p className="text-gray-300 text-sm mb-4">
                Advanced AI analysis with Wolf AI Pro for intelligent command validation.
              </p>
              <ul className="text-gray-400 text-sm space-y-1">
                <li>• Command syntax validation</li>
                <li>• Security risk assessment</li>
                <li>• Performance optimization tips</li>
                <li>• Alternative command suggestions</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Start Guide */}
      <section className="py-16 px-6 md:px-12">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-8 text-center">
            Quick Start Guide
          </h2>
          
          <div className="glassmorphism p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-psyco-green-DEFAULT font-semibold mb-4">Built-in Commands</h3>
                <div className="space-y-2 font-mono text-sm">
                  <div className="flex">
                    <span className="text-psyco-green-DEFAULT w-24">help</span>
                    <span className="text-gray-300">Show help message</span>
                  </div>
                  <div className="flex">
                    <span className="text-psyco-green-DEFAULT w-24">training</span>
                    <span className="text-gray-300">Toggle AI training mode</span>
                  </div>
                  <div className="flex">
                    <span className="text-psyco-green-DEFAULT w-24">commands</span>
                    <span className="text-gray-300">List all Linux commands</span>
                  </div>
                  <div className="flex">
                    <span className="text-psyco-green-DEFAULT w-24">explain</span>
                    <span className="text-gray-300">Get command details</span>
                  </div>
                  <div className="flex">
                    <span className="text-psyco-green-DEFAULT w-24">test</span>
                    <span className="text-gray-300">AI command validation</span>
                  </div>
                  <div className="flex">
                    <span className="text-psyco-green-DEFAULT w-24">clear</span>
                    <span className="text-gray-300">Clear terminal screen</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-psyco-green-DEFAULT font-semibold mb-4">Example Commands</h3>
                <div className="space-y-2 font-mono text-sm">
                  <div className="bg-psyco-black-card p-2 rounded">
                    <span className="text-gray-400">$</span> <span className="text-white">ls -la</span>
                  </div>
                  <div className="bg-psyco-black-card p-2 rounded">
                    <span className="text-gray-400">$</span> <span className="text-white">explain grep</span>
                  </div>
                  <div className="bg-psyco-black-card p-2 rounded">
                    <span className="text-gray-400">$</span> <span className="text-white">test chmod 755 file.sh</span>
                  </div>
                  <div className="bg-psyco-black-card p-2 rounded">
                    <span className="text-gray-400">$</span> <span className="text-white">commands network</span>
                  </div>
                </div>
                
                <div className="mt-4 p-3 bg-blue-500/20 border border-blue-500/50 rounded">
                  <p className="text-blue-300 text-sm">
                    💡 <strong>Tip:</strong> Use Tab for command completion and ↑/↓ arrows for command history navigation.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-6 md:px-12 bg-psyco-black-light">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-6">
            Ready to Master Linux Commands?
          </h2>
          <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
            Start your journey with our AI-powered terminal training system. Learn at your own pace 
            with intelligent feedback and comprehensive documentation.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button 
              onClick={() => document.querySelector('input')?.focus()}
              className="bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-black font-semibold py-3 px-8 rounded-lg transition-colors"
            >
              Start Training Now
            </button>
            <button 
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              className="border border-psyco-green-DEFAULT text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT hover:text-black font-semibold py-3 px-8 rounded-lg transition-colors"
            >
              Back to Top
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default TerminalTraining;
