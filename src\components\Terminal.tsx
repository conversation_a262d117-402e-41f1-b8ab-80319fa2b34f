import React, { useState, useEffect, useRef } from 'react';
import { Terminal as TerminalIcon, Co<PERSON>, Refresh<PERSON>w, BookOpen, Zap } from 'lucide-react';

interface TerminalLine {
  id: string;
  type: 'command' | 'output' | 'error' | 'info' | 'success';
  content: string;
  timestamp: Date;
}

interface LinuxCommand {
  command: string;
  description: string;
  syntax: string;
  examples: string[];
  category: string;
}

const Terminal: React.FC = () => {
  const [currentInput, setCurrentInput] = useState('');
  const [terminalHistory, setTerminalHistory] = useState<TerminalLine[]>([]);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isLoading, setIsLoading] = useState(false);
  const [currentUser] = useState('cyberwolf');
  const [currentPath] = useState('~/terminal-training');
  const [isTrainingMode, setIsTrainingMode] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showCommandList, setShowCommandList] = useState(false);

  const terminalRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Wolf AI Configuration
  const WOLF_AI_API_KEY = 'AIzaSyAG4Krqod06AT4rEtLh0ungd90xMzAml0A';
  const WOLF_AI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';

  // Linux Commands Database (A-Z)
  const linuxCommands: LinuxCommand[] = [
    // A Commands
    { command: 'alias', description: 'Create an alias for a command', syntax: 'alias name=command', examples: ['alias ll="ls -la"', 'alias grep="grep --color=auto"'], category: 'system' },
    { command: 'awk', description: 'Pattern scanning and processing language', syntax: 'awk pattern { action } file', examples: ['awk \'{print $1}\' file.txt', 'awk \'/pattern/ {print}\' file.txt'], category: 'text' },
    { command: 'apt', description: 'Package manager for Debian/Ubuntu', syntax: 'apt [options] command', examples: ['apt update', 'apt install package', 'apt remove package'], category: 'package' },

    // B Commands
    { command: 'bash', description: 'Bourne Again Shell', syntax: 'bash [options] [script]', examples: ['bash script.sh', 'bash -x script.sh'], category: 'shell' },
    { command: 'bg', description: 'Put jobs in the background', syntax: 'bg [job_spec]', examples: ['bg %1', 'bg'], category: 'process' },
    { command: 'basename', description: 'Strip directory and suffix from filenames', syntax: 'basename NAME [SUFFIX]', examples: ['basename /path/to/file.txt', 'basename /path/to/file.txt .txt'], category: 'file' },

    // C Commands
    { command: 'cat', description: 'Display file contents', syntax: 'cat [options] file', examples: ['cat file.txt', 'cat -n file.txt', 'cat file1 file2'], category: 'file' },
    { command: 'cd', description: 'Change directory', syntax: 'cd [directory]', examples: ['cd /home/<USER>', 'cd ..', 'cd ~'], category: 'navigation' },
    { command: 'chmod', description: 'Change file permissions', syntax: 'chmod [options] mode file', examples: ['chmod 755 script.sh', 'chmod +x file', 'chmod -R 644 directory/'], category: 'permissions' },
    { command: 'chown', description: 'Change file ownership', syntax: 'chown [options] owner[:group] file', examples: ['chown user:group file.txt', 'chown -R user directory/'], category: 'permissions' },
    { command: 'cp', description: 'Copy files or directories', syntax: 'cp [options] source destination', examples: ['cp file.txt backup.txt', 'cp -r directory/ backup/'], category: 'file' },
    { command: 'curl', description: 'Transfer data from or to a server', syntax: 'curl [options] URL', examples: ['curl https://example.com', 'curl -O https://example.com/file.zip'], category: 'network' },

    // D Commands
    { command: 'date', description: 'Display or set system date', syntax: 'date [options] [+format]', examples: ['date', 'date +"%Y-%m-%d %H:%M:%S"'], category: 'system' },
    { command: 'df', description: 'Display filesystem disk space usage', syntax: 'df [options] [filesystem]', examples: ['df -h', 'df -i'], category: 'system' },
    { command: 'diff', description: 'Compare files line by line', syntax: 'diff [options] file1 file2', examples: ['diff file1.txt file2.txt', 'diff -u file1 file2'], category: 'text' },
    { command: 'du', description: 'Display directory space usage', syntax: 'du [options] [directory]', examples: ['du -h', 'du -sh directory/'], category: 'system' },

    // E Commands
    { command: 'echo', description: 'Display text', syntax: 'echo [options] text', examples: ['echo "Hello World"', 'echo $HOME'], category: 'text' },
    { command: 'env', description: 'Display environment variables', syntax: 'env [options] [command]', examples: ['env', 'env | grep PATH'], category: 'system' },
    { command: 'exit', description: 'Exit the shell', syntax: 'exit [status]', examples: ['exit', 'exit 0'], category: 'shell' },

    // F Commands
    { command: 'find', description: 'Search for files and directories', syntax: 'find [path] [expression]', examples: ['find . -name "*.txt"', 'find /home -type f -size +1M'], category: 'search' },
    { command: 'fg', description: 'Bring jobs to foreground', syntax: 'fg [job_spec]', examples: ['fg %1', 'fg'], category: 'process' },
    { command: 'file', description: 'Determine file type', syntax: 'file [options] file', examples: ['file document.pdf', 'file *'], category: 'file' },

    // G Commands
    { command: 'grep', description: 'Search text patterns in files', syntax: 'grep [options] pattern [file]', examples: ['grep "pattern" file.txt', 'grep -r "pattern" directory/'], category: 'search' },
    { command: 'gunzip', description: 'Decompress gzip files', syntax: 'gunzip [options] file.gz', examples: ['gunzip file.gz', 'gunzip -c file.gz'], category: 'compression' },
    { command: 'gzip', description: 'Compress files', syntax: 'gzip [options] file', examples: ['gzip file.txt', 'gzip -9 file.txt'], category: 'compression' },

    // H Commands
    { command: 'head', description: 'Display first lines of a file', syntax: 'head [options] file', examples: ['head file.txt', 'head -n 20 file.txt'], category: 'text' },
    { command: 'history', description: 'Display command history', syntax: 'history [options]', examples: ['history', 'history 10'], category: 'shell' },
    { command: 'hostname', description: 'Display or set system hostname', syntax: 'hostname [options] [name]', examples: ['hostname', 'hostname -I'], category: 'network' },

    // I Commands
    { command: 'id', description: 'Display user and group IDs', syntax: 'id [options] [user]', examples: ['id', 'id username'], category: 'system' },
    { command: 'ifconfig', description: 'Configure network interface', syntax: 'ifconfig [interface] [options]', examples: ['ifconfig', 'ifconfig eth0'], category: 'network' },

    // J Commands
    { command: 'jobs', description: 'Display active jobs', syntax: 'jobs [options]', examples: ['jobs', 'jobs -l'], category: 'process' },

    // K Commands
    { command: 'kill', description: 'Terminate processes', syntax: 'kill [options] PID', examples: ['kill 1234', 'kill -9 1234'], category: 'process' },
    { command: 'killall', description: 'Kill processes by name', syntax: 'killall [options] name', examples: ['killall firefox', 'killall -9 process_name'], category: 'process' },

    // L Commands
    { command: 'ls', description: 'List directory contents', syntax: 'ls [options] [directory]', examples: ['ls', 'ls -la', 'ls -lh /home'], category: 'navigation' },
    { command: 'ln', description: 'Create links between files', syntax: 'ln [options] target link_name', examples: ['ln -s /path/to/file symlink', 'ln file hardlink'], category: 'file' },
    { command: 'locate', description: 'Find files by name', syntax: 'locate [options] pattern', examples: ['locate filename', 'locate "*.txt"'], category: 'search' },

    // M Commands
    { command: 'man', description: 'Display manual pages', syntax: 'man [section] command', examples: ['man ls', 'man 5 passwd'], category: 'help' },
    { command: 'mkdir', description: 'Create directories', syntax: 'mkdir [options] directory', examples: ['mkdir newdir', 'mkdir -p path/to/dir'], category: 'file' },
    { command: 'mv', description: 'Move/rename files and directories', syntax: 'mv [options] source destination', examples: ['mv file.txt newname.txt', 'mv file.txt /path/to/'], category: 'file' },

    // N Commands
    { command: 'nano', description: 'Text editor', syntax: 'nano [options] file', examples: ['nano file.txt', 'nano +10 file.txt'], category: 'editor' },
    { command: 'netstat', description: 'Display network connections', syntax: 'netstat [options]', examples: ['netstat -tuln', 'netstat -r'], category: 'network' },

    // O Commands
    { command: 'openssl', description: 'OpenSSL command line tool', syntax: 'openssl command [options]', examples: ['openssl version', 'openssl rand -hex 16'], category: 'security' },

    // P Commands
    { command: 'ps', description: 'Display running processes', syntax: 'ps [options]', examples: ['ps aux', 'ps -ef'], category: 'process' },
    { command: 'pwd', description: 'Print working directory', syntax: 'pwd', examples: ['pwd'], category: 'navigation' },
    { command: 'ping', description: 'Send ICMP echo requests', syntax: 'ping [options] destination', examples: ['ping google.com', 'ping -c 4 *******'], category: 'network' },

    // Q Commands
    { command: 'quota', description: 'Display disk usage and limits', syntax: 'quota [options] [user]', examples: ['quota', 'quota -u username'], category: 'system' },

    // R Commands
    { command: 'rm', description: 'Remove files and directories', syntax: 'rm [options] file', examples: ['rm file.txt', 'rm -rf directory/'], category: 'file' },
    { command: 'rmdir', description: 'Remove empty directories', syntax: 'rmdir [options] directory', examples: ['rmdir emptydir', 'rmdir -p path/to/empty'], category: 'file' },

    // S Commands
    { command: 'ssh', description: 'Secure Shell remote login', syntax: 'ssh [options] user@host', examples: ['ssh <EMAIL>', 'ssh -p 2222 user@host'], category: 'network' },
    { command: 'sudo', description: 'Execute commands as another user', syntax: 'sudo [options] command', examples: ['sudo apt update', 'sudo -u user command'], category: 'system' },
    { command: 'sort', description: 'Sort lines in text files', syntax: 'sort [options] file', examples: ['sort file.txt', 'sort -n numbers.txt'], category: 'text' },

    // T Commands
    { command: 'tail', description: 'Display last lines of a file', syntax: 'tail [options] file', examples: ['tail file.txt', 'tail -f /var/log/syslog'], category: 'text' },
    { command: 'tar', description: 'Archive files', syntax: 'tar [options] archive files', examples: ['tar -czf archive.tar.gz files/', 'tar -xzf archive.tar.gz'], category: 'compression' },
    { command: 'top', description: 'Display running processes', syntax: 'top [options]', examples: ['top', 'top -u username'], category: 'process' },
    { command: 'touch', description: 'Create empty files or update timestamps', syntax: 'touch [options] file', examples: ['touch newfile.txt', 'touch -t 202301011200 file.txt'], category: 'file' },

    // U Commands
    { command: 'uname', description: 'Display system information', syntax: 'uname [options]', examples: ['uname -a', 'uname -r'], category: 'system' },
    { command: 'uniq', description: 'Report or omit repeated lines', syntax: 'uniq [options] file', examples: ['uniq file.txt', 'sort file.txt | uniq'], category: 'text' },
    { command: 'uptime', description: 'Show system uptime and load', syntax: 'uptime', examples: ['uptime'], category: 'system' },

    // V Commands
    { command: 'vi', description: 'Vi text editor', syntax: 'vi [options] file', examples: ['vi file.txt', 'vi +10 file.txt'], category: 'editor' },
    { command: 'vim', description: 'Vim text editor', syntax: 'vim [options] file', examples: ['vim file.txt', 'vim +/pattern file.txt'], category: 'editor' },

    // W Commands
    { command: 'wc', description: 'Word, line, character, and byte count', syntax: 'wc [options] file', examples: ['wc file.txt', 'wc -l file.txt'], category: 'text' },
    { command: 'wget', description: 'Download files from web', syntax: 'wget [options] URL', examples: ['wget https://example.com/file.zip', 'wget -r https://example.com/'], category: 'network' },
    { command: 'which', description: 'Locate a command', syntax: 'which command', examples: ['which python', 'which -a gcc'], category: 'system' },
    { command: 'whoami', description: 'Display current username', syntax: 'whoami', examples: ['whoami'], category: 'system' },

    // X Commands
    { command: 'xargs', description: 'Execute commands from standard input', syntax: 'xargs [options] command', examples: ['find . -name "*.txt" | xargs grep "pattern"', 'echo "file1 file2" | xargs rm'], category: 'text' },

    // Y Commands
    { command: 'yes', description: 'Output a string repeatedly', syntax: 'yes [string]', examples: ['yes', 'yes "hello"'], category: 'text' },
    { command: 'yum', description: 'Package manager for Red Hat/CentOS', syntax: 'yum [options] command', examples: ['yum update', 'yum install package'], category: 'package' },

    // Z Commands
    { command: 'zip', description: 'Create zip archives', syntax: 'zip [options] archive files', examples: ['zip archive.zip file1 file2', 'zip -r archive.zip directory/'], category: 'compression' },
    { command: 'zcat', description: 'Display compressed file contents', syntax: 'zcat file.gz', examples: ['zcat file.txt.gz', 'zcat *.gz'], category: 'compression' }
  ];

  const categories = ['all', 'file', 'navigation', 'text', 'system', 'network', 'process', 'permissions', 'compression', 'search', 'editor', 'shell', 'package', 'security', 'help'];

  // Initialize terminal with welcome message
  useEffect(() => {
    const welcomeMessages: TerminalLine[] = [
      {
        id: '1',
        type: 'info',
        content: '🐺 Welcome to Cyber Wolf Terminal Training System',
        timestamp: new Date()
      },
      {
        id: '2',
        type: 'info',
        content: 'Type "help" for available commands or "training" to start interactive training',
        timestamp: new Date()
      },
      {
        id: '3',
        type: 'info',
        content: 'AI-powered command validation with Wolf AI integration',
        timestamp: new Date()
      }
    ];
    setTerminalHistory(welcomeMessages);
  }, []);

  // Auto-scroll to bottom
  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [terminalHistory]);

  // Focus input when terminal is clicked
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const addToHistory = (line: TerminalLine) => {
    setTerminalHistory(prev => [...prev, line]);
  };

  const getPrompt = () => {
    return `${currentUser}@cyberwolf:${currentPath}$`;
  };

  // Wolf AI Integration
  const validateCommandWithAI = async (command: string): Promise<string> => {
    try {
      const prompt = `
        As a Linux terminal expert, analyze this command: "${command}"

        Please provide:
        1. Is this a valid Linux command? (Yes/No)
        2. Brief explanation of what it does
        3. Any security concerns or warnings
        4. Suggest improvements if applicable
        5. Rate the command difficulty (Beginner/Intermediate/Advanced)

        Keep response concise and educational.
      `;

      const response = await fetch(`${WOLF_AI_API_URL}?key=${WOLF_AI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }]
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get AI response');
      }

      const data = await response.json();
      return data.candidates[0]?.content?.parts[0]?.text || 'AI analysis unavailable';
    } catch (error) {
      console.error('Wolf AI Error:', error);
      return 'AI analysis temporarily unavailable. Command processed locally.';
    }
  };

  // Command processing functions
  const findCommand = (cmd: string): LinuxCommand | undefined => {
    return linuxCommands.find(command => command.command === cmd.toLowerCase());
  };

  const getFilteredCommands = (): LinuxCommand[] => {
    if (selectedCategory === 'all') {
      return linuxCommands;
    }
    return linuxCommands.filter(cmd => cmd.category === selectedCategory);
  };

  const processBuiltinCommand = async (command: string, args: string[]): Promise<boolean> => {
    const cmd = command.toLowerCase();

    switch (cmd) {
      case 'help':
        addToHistory({
          id: Date.now().toString(),
          type: 'info',
          content: `
🐺 Cyber Wolf Terminal Training Commands:

Built-in Commands:
  help                    - Show this help message
  clear                   - Clear terminal screen
  training               - Toggle training mode
  commands [category]    - List available Linux commands
  explain <command>      - Get detailed command explanation
  test <command>         - Test command with AI validation
  categories             - Show command categories
  history                - Show command history
  exit                   - Exit terminal

Training Features:
  - AI-powered command validation
  - Interactive command learning
  - Real-time feedback and suggestions
  - Command difficulty ratings
  - Security warnings and best practices

Type any Linux command to see its documentation and examples!
          `,
          timestamp: new Date()
        });
        return true;

      case 'clear':
        setTerminalHistory([]);
        return true;

      case 'training':
        setIsTrainingMode(!isTrainingMode);
        addToHistory({
          id: Date.now().toString(),
          type: isTrainingMode ? 'error' : 'success',
          content: `Training mode ${!isTrainingMode ? 'enabled' : 'disabled'}. ${!isTrainingMode ? 'Commands will be validated with Wolf AI.' : ''}`,
          timestamp: new Date()
        });
        return true;

      case 'commands':
        const category = args[0] || 'all';
        const filteredCommands = category === 'all'
          ? linuxCommands
          : linuxCommands.filter(cmd => cmd.category === category);

        if (filteredCommands.length === 0) {
          addToHistory({
            id: Date.now().toString(),
            type: 'error',
            content: `No commands found for category: ${category}`,
            timestamp: new Date()
          });
        } else {
          const commandList = filteredCommands
            .map(cmd => `  ${cmd.command.padEnd(15)} - ${cmd.description}`)
            .join('\n');

          addToHistory({
            id: Date.now().toString(),
            type: 'output',
            content: `Linux Commands (${category}):\n${commandList}`,
            timestamp: new Date()
          });
        }
        return true;

      case 'categories':
        addToHistory({
          id: Date.now().toString(),
          type: 'info',
          content: `Available categories: ${categories.filter(c => c !== 'all').join(', ')}`,
          timestamp: new Date()
        });
        return true;

      case 'explain':
        if (args.length === 0) {
          addToHistory({
            id: Date.now().toString(),
            type: 'error',
            content: 'Usage: explain <command>',
            timestamp: new Date()
          });
          return true;
        }

        const cmdToExplain = findCommand(args[0]);
        if (cmdToExplain) {
          const explanation = `
Command: ${cmdToExplain.command}
Description: ${cmdToExplain.description}
Syntax: ${cmdToExplain.syntax}
Category: ${cmdToExplain.category}

Examples:
${cmdToExplain.examples.map(ex => `  $ ${ex}`).join('\n')}
          `;
          addToHistory({
            id: Date.now().toString(),
            type: 'info',
            content: explanation,
            timestamp: new Date()
          });
        } else {
          addToHistory({
            id: Date.now().toString(),
            type: 'error',
            content: `Command '${args[0]}' not found in database. Try 'commands' to see available commands.`,
            timestamp: new Date()
          });
        }
        return true;

      case 'test':
        if (args.length === 0) {
          addToHistory({
            id: Date.now().toString(),
            type: 'error',
            content: 'Usage: test <command>',
            timestamp: new Date()
          });
          return true;
        }

        setIsLoading(true);
        addToHistory({
          id: Date.now().toString(),
          type: 'info',
          content: `🐺 Analyzing command with Wolf AI: ${args.join(' ')}`,
          timestamp: new Date()
        });

        try {
          const aiResponse = await validateCommandWithAI(args.join(' '));
          addToHistory({
            id: Date.now().toString(),
            type: 'success',
            content: `Wolf AI Analysis:\n${aiResponse}`,
            timestamp: new Date()
          });
        } catch (error) {
          addToHistory({
            id: Date.now().toString(),
            type: 'error',
            content: 'Failed to get AI analysis. Please try again.',
            timestamp: new Date()
          });
        }
        setIsLoading(false);
        return true;

      case 'history':
        const historyList = commandHistory
          .slice(-10)
          .map((cmd, index) => `  ${commandHistory.length - 10 + index + 1}  ${cmd}`)
          .join('\n');

        addToHistory({
          id: Date.now().toString(),
          type: 'output',
          content: `Recent command history:\n${historyList}`,
          timestamp: new Date()
        });
        return true;

      case 'exit':
        addToHistory({
          id: Date.now().toString(),
          type: 'info',
          content: '👋 Thanks for using Cyber Wolf Terminal Training!',
          timestamp: new Date()
        });
        return true;

      default:
        return false;
    }
  };

  const processCommand = async (input: string) => {
    const trimmedInput = input.trim();
    if (!trimmedInput) return;

    // Add command to history
    setCommandHistory(prev => [...prev, trimmedInput]);
    setHistoryIndex(-1);

    // Add command to terminal display
    addToHistory({
      id: Date.now().toString(),
      type: 'command',
      content: `${getPrompt()} ${trimmedInput}`,
      timestamp: new Date()
    });

    const [command, ...args] = trimmedInput.split(' ');

    // Check if it's a built-in command
    const isBuiltin = await processBuiltinCommand(command, args);
    if (isBuiltin) {
      return;
    }

    // Check if it's a known Linux command
    const linuxCmd = findCommand(command);
    if (linuxCmd) {
      let output = `
📖 Command: ${linuxCmd.command}
📝 Description: ${linuxCmd.description}
🔧 Syntax: ${linuxCmd.syntax}
📂 Category: ${linuxCmd.category}

💡 Examples:
${linuxCmd.examples.map(ex => `   $ ${ex}`).join('\n')}
      `;

      addToHistory({
        id: Date.now().toString(),
        type: 'info',
        content: output,
        timestamp: new Date()
      });

      // If training mode is enabled, get AI validation
      if (isTrainingMode) {
        setIsLoading(true);
        addToHistory({
          id: Date.now().toString(),
          type: 'info',
          content: '🐺 Getting Wolf AI analysis...',
          timestamp: new Date()
        });

        try {
          const aiResponse = await validateCommandWithAI(trimmedInput);
          addToHistory({
            id: Date.now().toString(),
            type: 'success',
            content: `🐺 Wolf AI Training Feedback:\n${aiResponse}`,
            timestamp: new Date()
          });
        } catch (error) {
          addToHistory({
            id: Date.now().toString(),
            type: 'error',
            content: '❌ AI analysis failed. Command documentation shown above.',
            timestamp: new Date()
          });
        }
        setIsLoading(false);
      }
    } else {
      // Unknown command
      addToHistory({
        id: Date.now().toString(),
        type: 'error',
        content: `Command '${command}' not found. Type 'help' for available commands or 'commands' to see Linux commands.`,
        timestamp: new Date()
      });

      // In training mode, still try to get AI feedback for unknown commands
      if (isTrainingMode) {
        setIsLoading(true);
        addToHistory({
          id: Date.now().toString(),
          type: 'info',
          content: '🐺 Checking with Wolf AI for unknown command...',
          timestamp: new Date()
        });

        try {
          const aiResponse = await validateCommandWithAI(trimmedInput);
          addToHistory({
            id: Date.now().toString(),
            type: 'info',
            content: `🐺 Wolf AI Analysis:\n${aiResponse}`,
            timestamp: new Date()
          });
        } catch (error) {
          addToHistory({
            id: Date.now().toString(),
            type: 'error',
            content: '❌ AI analysis unavailable.',
            timestamp: new Date()
          });
        }
        setIsLoading(false);
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      processCommand(currentInput);
      setCurrentInput('');
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (commandHistory.length > 0) {
        const newIndex = historyIndex === -1 ? commandHistory.length - 1 : Math.max(0, historyIndex - 1);
        setHistoryIndex(newIndex);
        setCurrentInput(commandHistory[newIndex]);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (historyIndex !== -1) {
        const newIndex = historyIndex + 1;
        if (newIndex >= commandHistory.length) {
          setHistoryIndex(-1);
          setCurrentInput('');
        } else {
          setHistoryIndex(newIndex);
          setCurrentInput(commandHistory[newIndex]);
        }
      }
    } else if (e.key === 'Tab') {
      e.preventDefault();
      // Simple tab completion for commands
      const matches = linuxCommands.filter(cmd =>
        cmd.command.startsWith(currentInput.toLowerCase())
      );
      if (matches.length === 1) {
        setCurrentInput(matches[0].command);
      } else if (matches.length > 1) {
        const suggestions = matches.map(cmd => cmd.command).join(', ');
        addToHistory({
          id: Date.now().toString(),
          type: 'info',
          content: `Suggestions: ${suggestions}`,
          timestamp: new Date()
        });
      }
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      addToHistory({
        id: Date.now().toString(),
        type: 'success',
        content: '📋 Copied to clipboard!',
        timestamp: new Date()
      });
    });
  };

  const clearTerminal = () => {
    setTerminalHistory([]);
  };

  const getLineTypeClass = (type: string) => {
    switch (type) {
      case 'command':
        return 'text-psyco-green-DEFAULT font-mono';
      case 'output':
        return 'text-gray-300 font-mono whitespace-pre-wrap';
      case 'error':
        return 'text-red-400 font-mono';
      case 'info':
        return 'text-blue-400 font-mono whitespace-pre-wrap';
      case 'success':
        return 'text-green-400 font-mono whitespace-pre-wrap';
      default:
        return 'text-gray-300 font-mono';
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto bg-psyco-black-card rounded-lg border border-psyco-green-muted/30 overflow-hidden shadow-2xl">
      {/* Terminal Header */}
      <div className="bg-psyco-black-light border-b border-psyco-green-muted/30 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <TerminalIcon className="text-psyco-green-DEFAULT" size={24} />
            <div>
              <h2 className="text-white font-bold text-lg">🐺 Cyber Wolf Terminal Training</h2>
              <p className="text-gray-400 text-sm">Linux Commands A-Z with AI-Powered Learning</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Training Mode Toggle */}
            <button
              onClick={() => setIsTrainingMode(!isTrainingMode)}
              className={`flex items-center gap-2 px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                isTrainingMode
                  ? 'bg-psyco-green-DEFAULT text-black'
                  : 'bg-gray-600 text-white hover:bg-gray-500'
              }`}
            >
              <Zap size={16} />
              {isTrainingMode ? 'AI Training ON' : 'AI Training OFF'}
            </button>

            {/* Command List Toggle */}
            <button
              onClick={() => setShowCommandList(!showCommandList)}
              className="flex items-center gap-2 px-3 py-1 bg-blue-600 hover:bg-blue-500 text-white rounded-md text-sm font-medium transition-colors"
            >
              <BookOpen size={16} />
              Commands
            </button>

            {/* Clear Terminal */}
            <button
              onClick={clearTerminal}
              className="flex items-center gap-2 px-3 py-1 bg-red-600 hover:bg-red-500 text-white rounded-md text-sm font-medium transition-colors"
            >
              <RefreshCw size={16} />
              Clear
            </button>
          </div>
        </div>

        {/* Status Bar */}
        <div className="mt-3 flex items-center justify-between text-sm">
          <div className="flex items-center gap-4">
            <span className="text-gray-400">
              Status: <span className={isTrainingMode ? 'text-green-400' : 'text-yellow-400'}>
                {isTrainingMode ? 'Training Mode' : 'Documentation Mode'}
              </span>
            </span>
            <span className="text-gray-400">
              Commands: <span className="text-psyco-green-DEFAULT">{linuxCommands.length}</span>
            </span>
            <span className="text-gray-400">
              AI: <span className="text-green-400">Wolf AI Pro</span>
            </span>
          </div>

          {isLoading && (
            <div className="flex items-center gap-2 text-yellow-400">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-yellow-400 border-t-transparent"></div>
              <span>Wolf AI Processing...</span>
            </div>
          )}
        </div>
      </div>

      {/* Command List Sidebar */}
      {showCommandList && (
        <div className="bg-psyco-black-DEFAULT border-b border-psyco-green-muted/30 p-4">
          <div className="flex items-center gap-4 mb-4">
            <h3 className="text-white font-semibold">Linux Commands Reference</h3>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="bg-psyco-black-card border border-psyco-green-muted/50 text-white rounded px-3 py-1 text-sm"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 max-h-60 overflow-y-auto">
            {getFilteredCommands().map((cmd) => (
              <div
                key={cmd.command}
                className="bg-psyco-black-card border border-psyco-green-muted/30 rounded p-3 hover:border-psyco-green-DEFAULT/50 transition-colors cursor-pointer"
                onClick={() => {
                  setCurrentInput(`explain ${cmd.command}`);
                  inputRef.current?.focus();
                }}
              >
                <div className="flex items-center justify-between mb-1">
                  <span className="text-psyco-green-DEFAULT font-mono font-bold">{cmd.command}</span>
                  <span className="text-xs text-gray-500 bg-gray-700 px-2 py-1 rounded">
                    {cmd.category}
                  </span>
                </div>
                <p className="text-gray-300 text-xs">{cmd.description}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Terminal Content */}
      <div
        ref={terminalRef}
        className="bg-psyco-black-DEFAULT p-4 h-96 overflow-y-auto font-mono text-sm"
        onClick={() => inputRef.current?.focus()}
      >
        {/* Terminal History */}
        {terminalHistory.map((line) => (
          <div key={line.id} className="mb-1 group">
            <div className="flex items-start justify-between">
              <span className={getLineTypeClass(line.type)}>
                {line.content}
              </span>
              {line.type === 'output' && (
                <button
                  onClick={() => copyToClipboard(line.content)}
                  className="opacity-0 group-hover:opacity-100 ml-2 p-1 hover:bg-gray-700 rounded transition-all"
                  title="Copy to clipboard"
                >
                  <Copy size={12} className="text-gray-400" />
                </button>
              )}
            </div>
          </div>
        ))}

        {/* Current Input Line */}
        <div className="flex items-center">
          <span className="text-psyco-green-DEFAULT mr-2">{getPrompt()}</span>
          <input
            ref={inputRef}
            type="text"
            value={currentInput}
            onChange={(e) => setCurrentInput(e.target.value)}
            onKeyDown={handleKeyDown}
            className="flex-1 bg-transparent text-white outline-none font-mono"
            placeholder="Type a command... (try 'help' or 'training')"
            disabled={isLoading}
          />
        </div>

        {/* Loading Indicator */}
        {isLoading && (
          <div className="flex items-center gap-2 mt-2 text-yellow-400">
            <div className="animate-pulse">🐺</div>
            <span>Wolf AI is analyzing your command...</span>
          </div>
        )}
      </div>

      {/* Terminal Footer */}
      <div className="bg-psyco-black-light border-t border-psyco-green-muted/30 p-3">
        <div className="flex items-center justify-between text-xs text-gray-400">
          <div className="flex items-center gap-4">
            <span>Press ↑/↓ for history</span>
            <span>Tab for completion</span>
            <span>Ctrl+C to interrupt</span>
          </div>
          <div className="flex items-center gap-2">
            <span>Powered by</span>
            <span className="text-psyco-green-DEFAULT font-semibold">CyberWolf AI</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Terminal;